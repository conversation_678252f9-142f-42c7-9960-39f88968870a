/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <jni.h>

#include <fbjni/detail/Common.h>
#include <fbjni/detail/CoreClasses.h>
#include <fbjni/detail/Environment.h>
#include <fbjni/detail/Exceptions.h>
#include <fbjni/detail/Hybrid.h>
#include <fbjni/detail/Iterator.h>
#include <fbjni/detail/JWeakReference.h>
#include <fbjni/detail/Log.h>
#include <fbjni/detail/Meta.h>
#include <fbjni/detail/ReferenceAllocators.h>
#include <fbjni/detail/References.h>
#include <fbjni/detail/Registration.h>
