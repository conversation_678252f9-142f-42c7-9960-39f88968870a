/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <fbjni/fbjni.h>

#include <functional>

namespace facebook {
namespace jni {

struct JRunnable : public JavaClass<JRunnable> {
  static auto constexpr kJavaDescriptor = "Ljava/lang/Runnable;";
};

struct JNativeRunnable : public HybridClass<JNativeRunnable, JRunnable> {
 public:
  static auto constexpr kJavaDescriptor = "Lcom/facebook/jni/NativeRunnable;";

  JNativeRunnable(std::function<void()>&& runnable)
      : runnable_(std::move(runnable)) {}

  static void OnLoad() {
    registerHybrid({
        makeNativeMethod("run", JNativeRunnable::run),
    });
  }

  void run() {
    runnable_();
  }

 private:
  std::function<void()> runnable_;
};

} // namespace jni
} // namespace facebook
